"""SQLAlchemy ORM models generated from init-db schema."""
from datetime import datetime
from uuid import UUID, uuid4

from sqlalchemy import BigInteger, Boolean, Column, ForeignKey, Integer, String, Text, TIMESTAMP, JSON
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import Mapped, mapped_column

from .base import Base


class InterviewSession(Base):
    __tablename__ = "interview_sessions"

    id: Mapped[UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True, default=uuid4)
    admin_id: Mapped[int] = mapped_column(Integer, nullable=False)
    candidate_name: Mapped[str] = mapped_column(String(100), nullable=False)
    interviewer_name: Mapped[str] = mapped_column(String(100), nullable=False)
    job_title: Mapped[str] = mapped_column(String(100), nullable=False)
    candidate_token: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    interviewer_token: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    expires_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=False), nullable=False)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=False), default=datetime.utcnow)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    language: Mapped[str | None] = mapped_column(String(50), nullable=True)  # Added language field


class KeystrokeEvent(Base):
    __tablename__ = "keystroke_events"

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    session_id: Mapped[UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("interview_sessions.id", ondelete="CASCADE"))
    user_type: Mapped[str] = mapped_column(String(20), nullable=False)
    event_type: Mapped[str] = mapped_column(String(10), nullable=False)
    key_code: Mapped[int] = mapped_column(Integer, nullable=False)
    key_char: Mapped[str | None] = mapped_column(String(10))
    timestamp_ms: Mapped[int] = mapped_column(BigInteger, nullable=False)
    inter_key_interval: Mapped[int | None] = mapped_column(Integer)
    cursor_position: Mapped[dict | None] = mapped_column(JSON)
    selection_range: Mapped[dict | None] = mapped_column(JSON)
    modifiers: Mapped[dict | None] = mapped_column(JSON)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=False), default=datetime.utcnow)


class CodeChange(Base):
    __tablename__ = "code_changes"

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    session_id: Mapped[UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("interview_sessions.id", ondelete="CASCADE"))
    user_type: Mapped[str] = mapped_column(String(20), nullable=False)
    change_type: Mapped[str] = mapped_column(String(20), nullable=False)
    content: Mapped[str | None] = mapped_column(Text)
    position_start: Mapped[dict | None] = mapped_column(JSON)
    position_end: Mapped[dict | None] = mapped_column(JSON)
    timestamp_ms: Mapped[int] = mapped_column(BigInteger, nullable=False)
    full_code_snapshot: Mapped[str | None] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=False), default=datetime.utcnow)


class InterviewArtifact(Base):
    __tablename__ = "interview_artifacts"

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    session_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("interview_sessions.id", ondelete="CASCADE")
    )
    artifact_type: Mapped[str] = mapped_column(String(50), nullable=False)  # 'question' or 'expected_output'
    content: Mapped[str] = mapped_column(Text, nullable=False)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=False), default=datetime.utcnow)


class ExecutionEvent(Base):
    __tablename__ = "execution_events"

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    session_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("interview_sessions.id", ondelete="CASCADE")
    )
    artifact_id: Mapped[int | None] = mapped_column(
        BigInteger, ForeignKey("interview_artifacts.id"), nullable=True
    )
    user_type: Mapped[str] = mapped_column(String(20), nullable=False)
    code_snapshot: Mapped[str] = mapped_column(Text, nullable=False)
    execution_start_ms: Mapped[int] = mapped_column(BigInteger, nullable=False)
    execution_end_ms: Mapped[int | None] = mapped_column(BigInteger)
    output: Mapped[str | None] = mapped_column(Text)
    error_message: Mapped[str | None] = mapped_column(Text)
    memory_usage: Mapped[int | None] = mapped_column(Integer)
    cpu_time_ms: Mapped[int | None] = mapped_column(Integer)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=False), default=datetime.utcnow)


class SuspiciousEvent(Base):
    """Raw suspicious activity events captured during an interview (paste, window blur, hot keys, etc.)."""

    __tablename__ = "suspicious_events"

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    session_id: Mapped[UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("interview_sessions.id", ondelete="CASCADE"))
    user_type: Mapped[str] = mapped_column(String(20), nullable=False)  # candidate / interviewer
    event_type: Mapped[str] = mapped_column(String(30), nullable=False)
    raw_payload: Mapped[dict] = mapped_column(JSON, nullable=False)
    severity: Mapped[str | None] = mapped_column(String(10))            # low | medium | high
    explanation: Mapped[str | None] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=False), default=datetime.utcnow)
