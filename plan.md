# Real-time Collaborative Coding Interview Platform - POC

## Notes
- Platform is a proof-of-concept for AI-powered cheating detection in coding interviews.
- Data collection (keystrokes, code changes, execution events) is the highest priority.
- Tech stack: Next.js (React 19, TypeScript), Tailwind, Shadcn/ui, Monaco Editor, FastAPI, PostgreSQL, SQLAlchemy, Redis, Judge0, Docker Compose.
- Real-time code sync and execution for both candidate and interviewer via Socket.io.
- Security: Use secure tokens for session URLs; OAuth not required now.
- UI polish is appreciated but secondary to robust data logging.
- Architecture must be modular for future AI/ML integration.

## Task List
- [x] Set up project structure (frontend, backend, init-db.sql) 
- [x] Create docker-compose.yml for all services 
- [x] Backend: Implement FastAPI app skeleton 
- [x] Backend: Design and implement SQLAlchemy models for provided schema 
- [x] Backend: Implement admin login and session creation endpoints 
- [x] Backend: Implement WebSocket endpoints for real-time sync (Socket.io) 
- [x] Backend: Integrate go-judge for Python & Java code execution 
- [x] Backend: Implement event logging for keystrokes, code changes (full snapshots via sockets), execution events 
- [x] Backend: Replace Judge0 integration with go-judge (runner & REST API) 
- [x] docker-compose: Add `go-judge` service and remove `judge0` 
- [x] Backend: Implement GoJudgeService for Python & Java 
- [x] Frontend: Update UI to support language selection (Python/Java) 
- [x] Backend: Implement GET /sessions/{session_id}/latest_code endpoint to retrieve the most recent code snapshot 
- [ ] Backend: Optimize DB for high-frequency writes
- [x] Frontend: Set up Next.js app with Tailwind and Shadcn/ui 
- [x] Frontend: Implement admin dashboard (login, create sessions, generate URLs) 
- [x] Frontend: Implement candidate/interviewer interview pages with Monaco Editor 
- [x] Frontend: Implement real-time code sync via WebSocket 
- [x] Frontend: Capture and send keystroke/code change events (ensure fullCodeSnapshot is sent) from Monaco Editor 
- [x] Frontend: On interview page load, fetch and display latest code snapshot from GET /sessions/{session_id}/latest_code 
- [x] Frontend: Display execution output/errors in real-time 
- [x] Frontend: Professional UI/UX polish 
- [ ] Test multi-user, multi-session scenarios
- [ ] Verify comprehensive data logging in DB
- [x] Document setup and usage in README 

### Suspicious Activity Detection (Phase 2)
- [x] **DB**: Add `suspicious_events` table (raw facts) with `severity` & `explanation` columns
- [x] **Models**: Create SQLAlchemy `SuspiciousEvent` model
- [x] **Frontend**: Capture paste, window blur/focus, and hot-key events; emit `suspicious_event` via Socket.IO
- [x] **Backend**: Socket handler to persist raw event & enqueue Celery task
- [x] **Analysis Pipeline**
  - [x] Implement lightweight rule engine
  - [x] Integrate optional LLM scoring using Gemini-2.5-flash-lite (only when uncertain)
  - [x] (Infra) Introduce Celery worker for async analysis queue
- [x] **Broadcast**: Send `suspicious_alert` with severity & explanation to interviewer clients
- [ ] **UI**: Add “Suspicious Activity” accordion with live vertical timeline and expandable alert details
- [ ] **Testing**: Simulate paste & window-switch scenarios; verify alerts & DB logs
- [ ] **Docs**: Update README with environment vars and usage instructions

## Completed Milestones

### AI Cheating Detection (Phase 1) - COMPLETE
This phase built the foundational multi-agent AI analysis system using LangGraph and integrated it into the interviewer's UI.
- **[x] Backend: Setup LangGraph & Dependencies**
- **[x] Backend: Define LangGraph State & Schema**
- **[x] Backend: Implement LangGraph Agent Nodes** (Solution Generation, Initial Analysis, Expert Analysis)
- **[x] Backend: Build the LangGraph Workflow** with conditional routing.
- **[x] Backend: Integrate with Execution Flow** to trigger analysis asynchronously on code execution.
- **[x] Frontend: Update Interviewer UI** with collapsible `Accordion` for analysis display.
- **[x] Frontend: Trigger and Display AI Analysis** with loading states and formatted results.

### MLflow Integration & Asynchronous Refactoring - COMPLETE
This phase focused on robust experiment tracking and resolving critical performance issues.
- **[x] Integrate MLflow** for experiment tracking and deep LangGraph tracing.
- **[x] Configure MLflow server in `docker-compose.yml`** with a bind mount for persistent artifact storage, resolving 404 errors on trace views.
- **[x] Refactor AI analysis to run asynchronously** in a background thread, preventing backend freezes and ensuring a responsive UI.
- **[x] Fix WebSocket broadcasting** of analysis results to ensure the frontend is updated reliably.

### UI/UX Polish (June 2025) - COMPLETE
- **[x] Update navbar logo** and fix root route redirect.
- **[x] Define color palette and font stack** and integrate into Tailwind config.
- **[x] Create reusable UI components** (`Button`, `Card`, `Badge`).
- **[x] Polish Admin Dashboard** layout and session creation flow.
- **[x] Implement Code Persistence** on refresh for the interview page.
- **[x] Render AI analysis explanations as formatted markdown** in the frontend for improved readability.

## Current Goal
Now, in addition to the AI Analysis, I also want to be able to detect if the candidate pasted code in the editor. Now that pasted code might not necessarily indicate cheating since it maybe text that the candidate copied from the question presented by the interviewer. It could also be a variable name that the candidate copied from ealier section of their own code in the editor. So, I want us to be able to intelligently determine if the pasted text was an attempt at cheating by the candidate. Later, I would also like to extend this detection to other hot keys, like Alt + Tab and any other strange hot key combinations which may indicate that the candidate is trying to interact with a cheating software. I would like to present these alerts to the interviewer in the interview tools area, under a new collapsible section. I would like to present these alerts as a running vertical timeline with a brief line about each alert and an icon indicating the severity of each alert. Clicking on an alert should show more details about what was detected and why we think it is a cheating attempt. We would need an LLM call for analyzing for these events, especially large content paste events, where a simple check will not suffice.

## UI/UX Polishing Roadmap (June 2025)

| Priority | Task | Owner | Status |
|----------|------|-------|--------|
| P0 | Update navbar logo to `<> Code Guardians` | Cascade | ✅ Done (19 Jun 2025)
| P0 | Root route `/` should redirect to admin dashboard (or login) instead of 404 | Cascade | ✅ Done (19 Jun 2025)
| P1 | Define colour palette (primary, secondary, accent, neutral   shades) & add to Tailwind config | Cascade | ✅ Done (19 Jun 2025)
| P1 | Choose font stack (e.g. `Inter` for UI + `JetBrains Mono` for code) and import via `@next/font/google` | Cascade | ✅ Done (19 Jun 2025)
| P1 | Create reusable `Button`, `Card`, `Badge` components using new palette | Cascade | ✅ Done (19 Jun 2025)
| P1 | Polish Admin Dashboard layout (stats cards, session list as per mock-up) | Cascade | ✅ Done (19 Jun 2025)
| P1 | Admin Dashboard: Enhance session creation with auto-refresh and persistent, dismissible new session links panel | Cascade | ✅ Done (20 Jun 2025)
| P1 | Enrich session list with candidate & interviewer names, job title, creation date, and status | Cascade/Frontend | ✅ Done (20 Jun 2025)
| P2 | Add micro-animations with `framer-motion` (page transitions, card hover, button tap) | Cascade | ✅ Done (20 Jun 2025)
| P1 | Apply Shadcn UI "New York" style with blue theme variant | Cascade | ✅ Done (20 Jun 2025)
| P2 | Create dark theme variant matching palette | Cascade | ⏳

> **Note:** Frontend polish may require corresponding backend & DB changes (e.g. storing candidate names). Each change will be implemented incrementally with coordinated migrations.

## Backend API Roadmap (June 2025)

| Priority | Endpoint | Purpose | Owner | Status |
|----------|----------|---------|-------|--------|
| P0 | `GET /stats` | Counts for dashboard cards | Backend | Done |
| P0 | `POST /sessions` | Create session (accepts candidate_name, interviewer_name, job_title) | Backend | Done |
| P0 | `GET /sessions` | Paginated list with candidate & interviewer names, job title, created_at, is_active | Backend | ✅ Done |
| P0 | `keystroke` (WebSocket event) | Persist keystroke events | Backend | ✅ Done (via Sockets) |
| P0 | `code_change` (WebSocket event) | Persist code changes including full snapshots | Backend | ✅ Done (via Sockets) |
| P0 | `execution_result` (WebSocket event) | Persist compile/execute events & results | Backend | ✅ Done (via Sockets) |
| P0 | `GET /sessions/{id}/latest_code` | Retrieve latest code snapshot for a session | Backend | ✅ Done (20 Jun 2025) |
| P1 | `PATCH /sessions/{id}` | Mark session finished / archived | Backend | ⏳ Todo |
| P2 | `POST /auth/refresh` | JWT refresh flow | Backend | ⏳ Future |
| P2 | `CRUD /admin/users` | Manage admin accounts | Backend | ⏳ Future |