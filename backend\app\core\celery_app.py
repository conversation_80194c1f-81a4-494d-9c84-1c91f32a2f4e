"""Celery application instance.

Run worker with for example:
    celery -A backend.app.core.celery_app worker -l info

The BROKER_URL / RESULT_BACKEND are expected to point at the Redis
instance that already backs sessions.
"""
from __future__ import annotations

import os

from celery import Celery

REDIS_URL = os.getenv("REDIS_URL", "redis://redis:6379/0")

celery_app = Celery(
    "code_guardians_backend",
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=[
        "app.tasks.suspicious",  # register suspicious analysis tasks
    ],
)

# Generic configuration – eager result serialization, etc.
celery_app.conf.update(
    task_serializer="json",
    result_serializer="json",
    accept_content=["json"],
    task_track_started=True,
    worker_max_tasks_per_child=100,  # memory leak mitigation
)
