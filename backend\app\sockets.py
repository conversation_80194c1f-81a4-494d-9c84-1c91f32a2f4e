"""Socket.io server and event handlers."""
from datetime import datetime
from typing import Any, Dict
from uuid import UUID

import socketio
from sqlalchemy import update
from sqlalchemy.ext.asyncio import AsyncSession

from .core.db import async_session
from .core.logging import get_logger, LogContext, PerformanceLogger
from .middleware.logging import WebSocketLoggingMixin, db_logger
from .models.schemas import CodeChange, ExecutionEvent, InterviewSession, KeystrokeEvent, SuspiciousEvent

import os
REDIS_URL = os.getenv("REDIS_URL", "redis://redis:6379/0")

sio = socketio.AsyncServer(
    async_mode="asgi",
    cors_allowed_origins="*",
    message_queue=REDIS_URL,  # enables emit from Celery workers
)

# Initialize logging
logger = get_logger("websocket")
ws_logger = WebSocketLoggingMixin()


def background_session():
    """Yield a new session per call."""
    return async_session()


@sio.event
async def connect(sid, environ):  # noqa: D401
    """Handle WebSocket connection."""
    ws_logger.log_connection(sid, "connect")


@sio.event
async def authenticate(sid, environ):  # noqa: D401
    """Handle WebSocket authentication."""
    ws_logger.log_connection(sid, "authenticate")
    # reuse connect logic for optional authenticate event
    await connect(sid, environ)


@sio.event
async def disconnect(sid):  # noqa: D401
    """Handle WebSocket disconnection."""
    ws_logger.log_connection(sid, "disconnect")


@sio.event
async def join_session(sid, data: Dict[str, Any]):  # noqa: D401
    """Client requests to join a session room.

    Expected data: { "session_id": str }
    """
    session_id = data.get("session_id")
    if session_id is None:
        ws_logger.log_error("join_session", "Missing session_id in data", socket_id=sid)
        return

    room = f"session_{session_id}"
    await sio.enter_room(sid, room)

    # Get room members for debugging
    try:
        room_members = sio.manager.get_participants("/", room)  # Use default namespace "/"
        ws_logger.log_event("join_session", session_id,
                           socket_id=sid,
                           room=room,
                           room_member_count=len(room_members),
                           room_members=list(room_members))
    except Exception as e:
        ws_logger.log_event("join_session", session_id,
                           socket_id=sid,
                           room=room,
                           debug_error=str(e))


@sio.event
async def code_change(sid, data: Dict[str, Any]):  # noqa: D401
    """Persist code change and broadcast to room."""
    session_id = data.get("sessionId")
    room = f"session_{session_id}"

    ws_logger.log_event("code_change", session_id,
                       user_type=data.get("userType"),
                       change_type=data.get("changeType"),
                       socket_id=sid)

    await sio.emit("code_update", data, room=room, skip_sid=sid)

    # Persist asynchronously
    try:
        async with background_session() as db:  # type: AsyncSession
            db.add(CodeChange(
                session_id=session_id,
                user_type=data.get("userType"),
                change_type=data.get("changeType"),
                content=data.get("content"),
                position_start=data.get("positionStart"),
                position_end=data.get("positionEnd"),
                timestamp_ms=data.get("timestampMs"),
                full_code_snapshot=data.get("fullCodeSnapshot"),
                created_at=datetime.utcnow(),
            ))
            await db.commit()
            db_logger.log_query("INSERT", "code_changes", session_id=session_id)
    except Exception as e:
        ws_logger.log_error("code_change", f"Database error: {str(e)}", session_id, socket_id=sid)


@sio.event
async def keystroke(sid, data):  # noqa: D401
    """Handle keystroke events and persist to database."""
    session_id = data.get("sessionId")
    room = f"session_{session_id}"

    # Log keystroke event (debug level to avoid spam)
    logger.debug("Keystroke event",
                session_id=session_id,
                user_type=data.get("userType"),
                key_char=data.get("keyChar"),
                socket_id=sid)

    await sio.emit("keystroke_update", data, room=room, skip_sid=sid)

    try:
        async with background_session() as db:
            db.add(KeystrokeEvent(
                session_id=session_id,
                user_type=data.get("userType"),
                event_type=data.get("eventType"),
                key_code=data.get("keyCode"),
                key_char=data.get("keyChar"),
                timestamp_ms=data.get("timestampMs"),
                inter_key_interval=data.get("interKeyInterval"),
                cursor_position=data.get("cursorPosition"),
                selection_range=data.get("selectionRange"),
                modifiers=data.get("modifiers"),
                created_at=datetime.utcnow(),
            ))
            await db.commit()
    except Exception as e:
        ws_logger.log_error("keystroke", f"Database error: {str(e)}", session_id, socket_id=sid)


@sio.event
async def language_change(sid, data: Dict[str, Any]):
    """Update session language and broadcast to room."""
    session_id_str = data.get("sessionId")
    new_language = data.get("language")

    if not session_id_str or not new_language:
        ws_logger.log_error("language_change", "Missing sessionId or language in data",
                           socket_id=sid, data=data)
        return

    room = f"session_{session_id_str}"

    ws_logger.log_event("language_change", session_id_str,
                       new_language=new_language, socket_id=sid)

    try:
        session_uuid = UUID(session_id_str)
        async with background_session() as db:  # type: AsyncSession
            stmt = (
                update(InterviewSession)
                .where(InterviewSession.id == session_uuid)
                .values(language=new_language)
            )
            result = await db.execute(stmt)

            if result.rowcount == 0:
                ws_logger.log_error("language_change", f"No session found with ID {session_id_str}",
                                   session_id_str, socket_id=sid)
                return

            await db.commit()
            db_logger.log_query("UPDATE", "interview_sessions", session_id=session_id_str)

        await sio.emit("language_update", {"language": new_language}, room=room)
        logger.info("Language updated successfully",
                   session_id=session_id_str,
                   language=new_language,
                   room=room)

    except Exception as e:
        ws_logger.log_error("language_change", f"Error updating language: {str(e)}",
                           session_id_str, socket_id=sid)


@sio.event
@sio.event
async def suspicious_event(sid, data: Dict[str, Any]):  # noqa: D401
    """Handle suspicious activity event and persist raw record.

    Expected data fields (frontend):
        {
            "sessionId": str,
            "userType": "candidate" | "interviewer",
            "eventType": "paste" | "window_blur" | ...,
            "rawPayload": { ... }   # arbitrary JSON serialisable
        }
    """
    session_id = data.get("sessionId")
    room = f"session_{session_id}"

    ws_logger.log_event(
        "suspicious_event",
        session_id,
        user_type=data.get("userType"),
        event_type=data.get("eventType"),
        socket_id=sid,
    )

    # Immediately persist (analysis will run async later)
    # Ignore events from interviewer; we only track candidate activity
    if data.get("userType") == "interviewer":
        return

    try:
        async with background_session() as db:
            event_obj = SuspiciousEvent(
                session_id=session_id,
                user_type=data.get("userType"),
                event_type=data.get("eventType"),
                raw_payload=data.get("rawPayload"),
                created_at=datetime.utcnow(),
            )
            db.add(event_obj)
            await db.flush()  # assigns primary key
            event_id = event_obj.id
            await db.commit()
            db_logger.log_query("INSERT", "suspicious_events", session_id=session_id)
    except Exception as e:
        ws_logger.log_error("suspicious_event", f"Database error: {str(e)}", session_id, socket_id=sid)
        return

    # Queue Celery analysis task
    try:
        from app.tasks.suspicious import analyze_suspicious_event  # local import to avoid celery in WS workers
        analyze_suspicious_event.delay(event_id)
    except Exception as e:  # noqa: BLE001
        logger.error("Failed to enqueue suspicious analysis task", error=str(e))

    # Notify interviewer that event received (pending analysis)
    await sio.emit(
        "suspicious_event_received",
        {"eventId": event_id, "eventType": data.get("eventType"), "pending": True},
        room=room,
        skip_sid=sid,
    )



async def execution_result(sid, data):  # noqa: D401
    """Handle code execution results and persist to database."""
    session_id = data.get("sessionId")
    room = f"session_{session_id}"

    ws_logger.log_event("execution_result", session_id,
                       user_type=data.get("userType"),
                       has_output=bool(data.get("output")),
                       has_error=bool(data.get("error")),
                       socket_id=sid)

    await sio.emit("execution_result", data, room=room, skip_sid=sid)

    try:
        async with background_session() as db:
            db.add(ExecutionEvent(
                session_id=session_id,
                user_type=data.get("userType"),
                code_snapshot=data.get("codeSnapshot"),
                execution_start_ms=data.get("executionStartMs"),
                execution_end_ms=data.get("executionEndMs"),
                output=data.get("output"),
                error_message=data.get("error"),
                memory_usage=data.get("memoryUsage"),
                cpu_time_ms=data.get("cpuTimeMs"),
                created_at=datetime.utcnow(),
            ))
            await db.commit()
            db_logger.log_query("INSERT", "execution_events", session_id=session_id)
    except Exception as e:
        ws_logger.log_error("execution_result", f"Database error: {str(e)}",
                           session_id, socket_id=sid)
