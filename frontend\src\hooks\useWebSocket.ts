import { useEffect, useRef } from "react";
import { io, Socket } from "socket.io-client";

interface Options {
  onCodeUpdate?: (data: any) => void;
  onKeystrokeUpdate?: (data: any) => void;
  onExecutionResult?: (payload: { output: string; error: string }) => void;
  onLanguageUpdate?: (payload: { language: 'python' | 'java' }) => void;
  onArtifactPresented?: (payload: { artifact_type: 'question' | 'expected_output', content: string, id: number }) => void;
  onAnalysisComplete?: (payload: any) => void;
  onAnalysisProgress?: (payload: { message: string }) => void;
  onSuspiciousEventReceived?: (payload: { eventId: number; eventType: string; pending?: boolean }) => void;
  onSuspiciousAlert?: (payload: any) => void;
}

export function useWebSocket(sessionId: string, options: Options = {}) {
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    if (!sessionId) return;
    const baseUrl = process.env.NEXT_PUBLIC_WS_URL || "http://localhost:8000";
    const socket = io(baseUrl, {
      path: "/ws/socket.io",
      transports: ["websocket"],
    });
    socketRef.current = socket;

    socket.on("connect", () => {
      socket.emit("join_session", { session_id: sessionId }); // server maps to room
    });

    if (options.onCodeUpdate) socket.on("code_update", options.onCodeUpdate);
    if (options.onKeystrokeUpdate) socket.on("keystroke_update", options.onKeystrokeUpdate);
    if (options.onExecutionResult) socket.on("execution_result", options.onExecutionResult);
    if (options.onLanguageUpdate) socket.on("language_update", options.onLanguageUpdate);
    if (options.onArtifactPresented) socket.on("artifact_presented", options.onArtifactPresented);
    if (options.onAnalysisComplete) socket.on("analysis_complete", options.onAnalysisComplete);
    if (options.onAnalysisProgress) socket.on("analysis_progress", options.onAnalysisProgress);
    if (options.onSuspiciousEventReceived) socket.on("suspicious_event_received", options.onSuspiciousEventReceived);
    if (options.onSuspiciousAlert) socket.on("suspicious_alert", options.onSuspiciousAlert);

    return () => {
      socket.disconnect();
    };
  }, [sessionId]);

  const emitCodeChange = (payload: any) => {
    socketRef.current?.emit("code_change", payload);
  };
  const emitKeystroke = (payload: any) => {
    socketRef.current?.emit("keystroke", payload);
  };
  const emitExecutionResult = (payload: any) => {
    socketRef.current?.emit("execution_result", payload);
  };

  const emitLanguageChange = (payload: any) => {
    socketRef.current?.emit("language_change", payload);
  };

  const emitSuspiciousEvent = (payload: any) => {
    socketRef.current?.emit("suspicious_event", payload);
  };

  return { emitCodeChange, emitKeystroke, emitExecutionResult, emitLanguageChange, emitSuspiciousEvent };
}
