# AI Code Analysis System

## Overview

The Code Guardians platform employs a sophisticated AI-powered analysis system to detect cheating attempts in coding interviews. The system combines rule-based logic with advanced Large Language Models (LLMs) to provide accurate, fast, and cost-effective suspicious activity detection.

## Architecture Components

### 1. Rule Engine (Fast Path)
- **Purpose**: Handle simple, deterministic events without AI overhead
- **Processing Time**: < 100ms
- **Use Cases**: Window blur duration, basic keystroke patterns
- **Cost**: Zero AI API costs

### 2. LLM Analysis (Complex Path)
- **Purpose**: Analyze complex events requiring contextual understanding
- **Processing Time**: 3-8 seconds
- **Use Cases**: Code paste analysis, complex behavioral patterns
- **Models**: Multiple LLMs via OpenRouter API

### 3. LangGraph Multi-Agent System
- **Framework**: LangGraph for orchestrating multiple AI agents
- **Flexibility**: Different agents for different analysis types
- **Scalability**: Easy to add new analysis capabilities

## AI Analysis Flow

```mermaid
flowchart TD
    A[Suspicious Event] --> B{Rule Engine Check}

    B -->|Simple Event| C[Rule-Based Classification]
    B -->|Complex Event| D[LLM Analysis Required]

    C --> E[Quick Result]
    C --> F[Update Database]

    D --> G[LangGraph Agent Selection]
    G --> H[Code Analysis Agent]
    G --> I[Behavior Analysis Agent]
    G --> J[Context Analysis Agent]

    H --> K[OpenRouter API Call]
    I --> K
    J --> K

    K --> L[LLM Response Processing]
    L --> M[Structured Output Parsing]
    M --> N[Result Validation]

    N --> O[Final Classification]
    O --> F

    F --> P[Redis Pub/Sub]
    P --> Q[Real-time UI Update]

    style C fill:#e8f5e8
    style K fill:#fff3e0
    style P fill:#f3e5f5
```

## Rule Engine Logic

### Window Blur Events
```python
def classify_window_blur(duration_seconds):
    if duration_seconds < 3:
        return "low", f"Brief window out of focus for {duration_seconds:.1f}s"
    elif duration_seconds < 10:
        return "medium", f"Window out of focus for {duration_seconds:.1f}s"
    else:
        return "high", f"Extended window out of focus for {duration_seconds:.1f}s"
```

### Paste Events
- **Always trigger LLM analysis** for contextual code understanding
- **Rule engine** only validates basic structure (non-empty content)
- **LLM determines** if paste contains complete solutions vs. snippets

## LLM Integration Architecture

```mermaid
sequenceDiagram
    participant CE as Celery Worker
    participant LG as LangGraph
    participant OR as OpenRouter API
    participant LLM as LLM Models

    CE->>LG: Analyze suspicious event
    LG->>LG: Select appropriate agent
    LG->>OR: Structured prompt + event data
    OR->>LLM: Route to best model
    LLM->>OR: Analysis response
    OR->>LG: Structured JSON response
    LG->>LG: Parse & validate output
    LG->>CE: Final classification result

    Note over LLM: Models: GPT-4, Claude-3.5,<br/>Gemini Pro, etc.
```

### Prompt Engineering

#### Code Paste Analysis Prompt
```
You are an expert at detecting cheating in coding interviews.
Analyze this paste event and determine if it represents potential cheating.

Event Details:
- Event Type: {event_type}
- Pasted Content: {pasted_text}
- Interview Context: {interview_context}
- Timing: {event_timing}

Classify the severity:
- LOW: Minor snippets, common patterns, debugging
- MEDIUM: Partial solutions, significant code blocks
- HIGH: Complete solutions, complex algorithms

Provide:
1. Severity level (low/medium/high)
2. Brief explanation (max 200 chars)

Respond in JSON format:
{
  "severity": "high",
  "explanation": "Complete solution with advanced algorithm implementation"
}
```

#### Multi-Agent Specialization

```mermaid
graph TB
    subgraph "LangGraph Multi-Agent System"
        MA[Main Orchestrator]

        subgraph "Specialized Agents"
            CA[Code Analysis Agent]
            BA[Behavior Analysis Agent]
            TA[Timing Analysis Agent]
            PA[Pattern Analysis Agent]
        end

        subgraph "LLM Models"
            GPT[GPT-4 Turbo]
            Claude[Claude-3.5 Sonnet]
            Gemini[Gemini Pro]
        end
    end

    MA --> CA
    MA --> BA
    MA --> TA
    MA --> PA

    CA --> GPT
    BA --> Claude
    TA --> Gemini
    PA --> GPT

    style MA fill:#e1f5fe
    style CA fill:#f3e5f5
    style BA fill:#e8f5e8
    style TA fill:#fff3e0
    style PA fill:#fce4ec
```

### Agent Responsibilities

1. **Code Analysis Agent**
   - Analyzes pasted code complexity
   - Detects complete vs. partial solutions
   - Identifies advanced algorithms

2. **Behavior Analysis Agent**
   - Patterns in typing speed
   - Window switching frequency
   - Time between events

3. **Timing Analysis Agent**
   - Correlates events with interview progress
   - Detects suspicious timing patterns
   - Analyzes response times

4. **Pattern Analysis Agent**
   - Historical behavior patterns
   - Cross-session analysis
   - Anomaly detection

## Structured Output Processing

### Pydantic Models
```python
class SuspiciousEventAnalysis(BaseModel):
    severity: Literal["low", "medium", "high"]
    explanation: str = Field(max_length=200)
    confidence: float = Field(ge=0.0, le=1.0)
    reasoning: Optional[str] = None
```

### JSON Output Parser
```python
from langchain.output_parsers import JsonOutputParser

parser = JsonOutputParser(pydantic_object=SuspiciousEventAnalysis)
```

### Error Handling & Validation
- **Schema Validation**: Ensures LLM output matches expected structure
- **Fallback Logic**: Default classification if LLM fails
- **Retry Mechanism**: Automatic retries for transient failures

## Performance Optimization

### Cost Optimization Strategy

```mermaid
pie title AI Analysis Cost Distribution
    "Rule Engine (Free)" : 65
    "LLM Analysis" : 35
```

**Savings**: 65% of events handled by rule engine (zero cost)
**Quality**: Complex events get full LLM analysis

### Response Time Optimization

| Event Type | Processing Method | Avg Time | Cost |
|------------|------------------|----------|------|
| Window Blur | Rule Engine | 50ms | $0 |
| Simple Paste | Rule Engine + Quick LLM | 2s | $0.001 |
| Complex Paste | Full LLM Analysis | 5s | $0.01 |
| Behavioral Pattern | Multi-Agent Analysis | 8s | $0.02 |

### Caching Strategy
- **Event Signatures**: Cache analysis for identical events
- **Pattern Recognition**: Cache common behavioral patterns
- **Model Responses**: Cache for similar code snippets

## Model Selection & Routing

### OpenRouter Integration
```python
# Dynamic model selection based on event complexity
def select_model(event_type, complexity_score):
    if complexity_score < 0.3:
        return "openai/gpt-3.5-turbo"  # Fast, cheap
    elif complexity_score < 0.7:
        return "anthropic/claude-3-haiku"  # Balanced
    else:
        return "openai/gpt-4-turbo"  # Best quality
```

### Model Characteristics

| Model | Strengths | Use Cases | Cost | Speed |
|-------|-----------|-----------|------|-------|
| GPT-4 Turbo | Code understanding | Complex paste analysis | High | Medium |
| Claude-3.5 Sonnet | Reasoning | Behavioral analysis | Medium | Fast |
| Gemini Pro | Pattern recognition | Timing analysis | Low | Fast |
| GPT-3.5 Turbo | General purpose | Simple classifications | Low | Very Fast |

## Quality Assurance

### Confidence Scoring
- **High Confidence** (>0.8): Direct classification
- **Medium Confidence** (0.5-0.8): Additional validation
- **Low Confidence** (<0.5): Human review flagged

### Validation Pipeline
```mermaid
flowchart LR
    A[LLM Response] --> B[Schema Validation]
    B --> C[Confidence Check]
    C --> D{Confidence > 0.8?}
    D -->|Yes| E[Accept Result]
    D -->|No| F[Secondary Analysis]
    F --> G[Ensemble Voting]
    G --> H[Final Decision]
```

### Continuous Learning
- **Feedback Loop**: Interviewer feedback improves model selection
- **A/B Testing**: Compare different models and prompts
- **Performance Monitoring**: Track accuracy and false positive rates

## Monitoring & Analytics

### Real-time Metrics
- **Analysis Success Rate**: % of successful LLM calls
- **Average Response Time**: Per model and event type
- **Cost Tracking**: API usage and expenses
- **Accuracy Metrics**: Based on interviewer feedback

### Error Tracking
- **API Failures**: OpenRouter service issues
- **Parsing Errors**: Invalid JSON responses
- **Timeout Handling**: Long-running analysis tasks
- **Rate Limiting**: API quota management

This AI analysis system provides intelligent, scalable, and cost-effective suspicious activity detection while maintaining high accuracy and fast response times.