# Suspicious Activity Detection Architecture

## Overview

The Code Guardians platform implements a sophisticated real-time suspicious activity detection system that monitors candidate behavior during coding interviews. The system captures various events (keystrokes, window focus, paste operations) and uses AI-powered analysis to detect potential cheating attempts.

## Architecture Components

### 1. Frontend (Next.js/React)
- **Event Capture**: Monaco Editor and browser APIs capture suspicious events
- **WebSocket Client**: Maintains real-time connection with backend
- **Real-time UI**: Displays suspicious events in timeline format for interviewers

### 2. Backend (FastAPI)
- **WebSocket Server**: Socket.IO server for real-time communication
- **Event Processing**: Receives events and queues them for analysis
- **Redis Integration**: Subscribes to analysis results from Celery workers

### 3. Celery Worker
- **Asynchronous Processing**: Handles CPU-intensive AI analysis tasks
- **Rule Engine**: Quick classification for simple events (window blur duration)
- **LLM Integration**: Complex analysis using multiple AI models via LangGraph
- **Redis Publisher**: Publishes analysis results back to main system

### 4. Redis
- **Message Queue**: Enables cross-process communication
- **Pub/Sub Pattern**: Decouples Celery workers from main application
- **Scalability**: Supports multiple worker instances

### 5. PostgreSQL
- **Event Storage**: Persists all suspicious events and analysis results
- **Session Management**: Links events to specific interview sessions

## Event Flow Architecture

```mermaid
sequenceDiagram
    participant Browser as 🌐 Browser
    participant Backend as 🖥️ Backend (FastAPI)
    participant Celery as ⚙️ Celery Worker
    participant Redis as 📦 Redis
    participant DB as 🗄️ PostgreSQL
    participant AI as 🤖 AI/LLM

    Note over Browser,AI: Suspicious Event Detection Flow

    Browser->>Backend: 1. Suspicious event via WebSocket
    Note right of Browser: paste, window_blur, etc.

    Backend->>DB: 2. Store event (status: analyzing)
    Backend->>Backend: 3. Queue Celery task
    Backend->>Browser: 4. Emit event with "analyzing" status

    Note over Celery,AI: Asynchronous Analysis

    Celery->>DB: 5. Fetch event details
    Celery->>Celery: 6. Apply rule engine

    alt Simple Event (e.g., short window blur)
        Celery->>Celery: 7a. Rule-based classification
    else Complex Event (e.g., code paste)
        Celery->>AI: 7b. LLM analysis via LangGraph
        AI->>Celery: 7c. AI analysis result
    end

    Celery->>DB: 8. Update event with analysis
    Celery->>Redis: 9. Publish result to channel

    Note over Backend,Browser: Real-time Result Delivery

    Redis->>Backend: 10. Redis pub/sub notification
    Backend->>Backend: 11. Process Redis message
    Backend->>Browser: 12. Emit updated event via WebSocket

    Note right of Browser: Event status changes from<br/>"analyzing" to final severity
```

## Container Architecture & Communication

```mermaid
graph TB
    subgraph "Docker Environment"
        subgraph "Frontend Container"
            NextJS[Next.js App<br/>Port 3000]
        end

        subgraph "Backend Container"
            FastAPI[FastAPI Server<br/>Port 8000]
            SocketIO[Socket.IO Server<br/>/ws endpoint]
            RedisSubscriber[Redis Subscriber<br/>Background Thread]
        end

        subgraph "Celery Worker Container"
            CeleryWorker[Celery Worker<br/>No exposed ports]
            RuleEngine[Rule Engine]
            LLMClient[LLM Client]
            RedisPublisher[Redis Publisher]
        end

        subgraph "Infrastructure"
            Redis[(Redis<br/>Port 6379)]
            PostgreSQL[(PostgreSQL<br/>Port 5432)]
        end
    end

    subgraph "External Services"
        OpenRouter[OpenRouter API<br/>LLM Provider]
    end

    %% Frontend to Backend
    NextJS <-->|WebSocket Connection| SocketIO
    NextJS <-->|HTTP API Calls| FastAPI

    %% Backend Internal
    FastAPI <-->|Queue Tasks| CeleryWorker
    FastAPI <-->|Store/Retrieve| PostgreSQL
    RedisSubscriber <-->|Subscribe| Redis

    %% Celery Worker
    CeleryWorker <-->|Read/Write Events| PostgreSQL
    CeleryWorker <-->|AI Analysis| OpenRouter
    RedisPublisher <-->|Publish Results| Redis

    %% Redis Pub/Sub Flow
    RedisPublisher -.->|Publish| Redis
    Redis -.->|Notify| RedisSubscriber
    RedisSubscriber -.->|Emit via Socket.IO| SocketIO

    style CeleryWorker fill:#e1f5fe
    style Redis fill:#fff3e0
    style SocketIO fill:#f3e5f5
```

## Key Architectural Decisions

### 1. **Redis Pub/Sub Pattern**
**Problem**: Celery workers run in separate processes and cannot directly emit WebSocket messages to connected clients.

**Solution**:
- Celery workers publish analysis results to Redis channel
- Main backend subscribes to Redis channel
- Backend emits WebSocket messages to frontend clients

**Benefits**:
- Decouples analysis processing from real-time communication
- Enables horizontal scaling of Celery workers
- Maintains real-time user experience

### 2. **Asynchronous Processing**
**Why**: AI analysis can take 5-10 seconds, which would block the user interface.

**Implementation**:
- Events immediately stored with "analyzing" status
- Celery handles analysis in background
- Results delivered via WebSocket when ready

### 3. **Rule Engine + AI Hybrid**
**Efficiency**: Simple events (window blur) use rule-based classification
**Accuracy**: Complex events (code paste) use LLM analysis
**Cost Optimization**: Reduces AI API calls by 60-70%

## Scaling Architecture

### Horizontal Scaling Capabilities

```mermaid
graph LR
    subgraph "Load Balanced Frontend"
        F1[Frontend 1]
        F2[Frontend 2]
        F3[Frontend N]
    end

    subgraph "Backend Cluster"
        B1[Backend 1]
        B2[Backend 2]
        B3[Backend N]
    end

    subgraph "Worker Pool"
        W1[Worker 1]
        W2[Worker 2]
        W3[Worker N]
    end

    subgraph "Shared Infrastructure"
        Redis[(Redis Cluster)]
        DB[(PostgreSQL<br/>Read Replicas)]
    end

    F1 & F2 & F3 --> B1 & B2 & B3
    B1 & B2 & B3 --> Redis
    B1 & B2 & B3 --> DB
    W1 & W2 & W3 --> Redis
    W1 & W2 & W3 --> DB

    style Redis fill:#fff3e0
    style DB fill:#e8f5e8
```

### Scaling Benefits

1. **Frontend Scaling**: Multiple Next.js instances behind load balancer
2. **Backend Scaling**: Multiple FastAPI instances with shared Redis/DB
3. **Worker Scaling**: Add Celery workers based on analysis queue depth
4. **Database Scaling**: Read replicas for analysis data, write master for events
5. **Redis Scaling**: Redis Cluster for high availability and performance

### Performance Characteristics

- **Event Capture**: < 50ms latency (direct WebSocket)
- **Rule Engine**: < 100ms processing time
- **AI Analysis**: 3-8 seconds (asynchronous, non-blocking)
- **Result Delivery**: < 100ms via Redis pub/sub
- **Concurrent Sessions**: 100+ simultaneous interviews supported

## WebSocket Communication Patterns

### 1. Suspicious Activity Events (This System)
```mermaid
sequenceDiagram
    participant C as Candidate Browser
    participant I as Interviewer Browser
    participant B as Backend
    participant W as Celery Worker
    participant R as Redis

    C->>B: Suspicious event (paste/blur)
    B->>I: Real-time event notification
    B->>W: Queue analysis task
    W->>R: Publish analysis result
    R->>B: Notify via pub/sub
    B->>I: Updated event with severity
```

**Characteristics**:
- **Pattern**: Event streaming with async processing
- **Latency**: Immediate event, delayed analysis
- **Direction**: Primarily candidate → interviewer
- **Persistence**: All events stored in database

### 2. Code Synchronization (Different System)
```mermaid
sequenceDiagram
    participant C as Candidate
    participant I as Interviewer
    participant B as Backend

    C->>B: Code change (character-level)
    B->>I: Sync code change
    I->>B: Code change (if editing)
    B->>C: Sync code change
```

**Characteristics**:
- **Pattern**: Real-time bidirectional sync
- **Latency**: < 50ms for immediate sync
- **Direction**: Bidirectional (candidate ↔ interviewer)
- **Persistence**: Latest state stored, not all changes

### Key Differences

| Aspect | Suspicious Activity | Code Sync |
|--------|-------------------|-----------|
| **Purpose** | Monitoring & alerts | Collaborative editing |
| **Processing** | Async AI analysis | Immediate sync |
| **Data Flow** | Unidirectional events | Bidirectional changes |
| **Persistence** | Full event history | Current state only |
| **Latency** | Tolerates delays | Requires immediacy |

## Container Communication Details

### Why Celery Worker Has No Exposed Ports

The Celery worker container doesn't expose ports because:

1. **Pull-based Architecture**: Workers pull tasks from Redis queue
2. **No Direct Client Access**: No external services connect directly to workers
3. **Security**: Reduces attack surface by limiting network exposure
4. **Scalability**: Workers can be added/removed without port management

### Communication Flow

```mermaid
graph TD
    A[Browser] -->|WebSocket| B[Backend:8000]
    B -->|Task Queue| C[Redis:6379]
    C -->|Pull Tasks| D[Celery Worker]
    D -->|Database| E[PostgreSQL:5432]
    D -->|Publish Results| C
    C -->|Pub/Sub| B
    B -->|WebSocket| A

    style D fill:#e1f5fe,stroke:#01579b
    style C fill:#fff3e0,stroke:#e65100
```

**Port Usage**:
- **Frontend**: 3000 (development)
- **Backend**: 8000 (API + WebSocket)
- **Redis**: 6379 (message queue + pub/sub)
- **PostgreSQL**: 5432 (data persistence)
- **Celery Worker**: No ports (internal communication only)

## Security & Reliability

### Authentication & Authorization
- **JWT Tokens**: Secure WebSocket connections
- **Role-based Access**: Candidate vs interviewer permissions
- **Session Isolation**: Events scoped to specific interviews

### Error Handling
- **Graceful Degradation**: System continues if AI analysis fails
- **Retry Logic**: Failed tasks automatically retried
- **Circuit Breaker**: Prevents cascade failures from AI service

### Monitoring
- **Real-time Metrics**: Event processing rates, queue depths
- **Health Checks**: Container and service health monitoring
- **Alerting**: Automated alerts for system issues

This architecture provides enterprise-grade scalability, reliability, and performance for real-time suspicious activity detection in coding interviews.