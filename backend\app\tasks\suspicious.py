"""Celery tasks for suspicious activity analysis."""
from __future__ import annotations

import json
import os
from typing import Tuple, Optional

from celery import shared_task
from sqlalchemy import update
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from sqlalchemy.dialects.postgresql import insert

from app.core.celery_app import celery_app  # ensures app is initialised
from app.core.logging import get_logger
from app.models.schemas import SuspiciousEvent, InterviewArtifact

from app.sockets import sio  # to emit alerts via message_queue
from app.services.ai_analysis_service import get_chat_model
from app.tasks.utils import similarity_ratio

logger = get_logger("suspicious_task")

# Celery worker runs in a synchronous context, so we need a sync driver
raw_db_url = os.getenv("DATABASE_URL", "postgresql+asyncpg://user:password@postgres:5432/interview_platform")
if "+asyncpg" in raw_db_url:
    raw_db_url = raw_db_url.replace("+asyncpg", "+psycopg2")
DATABASE_URL = raw_db_url
engine = create_engine(DATABASE_URL, isolation_level="AUTOCOMMIT")


# ---------------- Simple rule engine ---------------- #

def apply_rules(event: SuspiciousEvent, question_text: str | None, expected_output: str | None, full_code_before: str | None) -> Tuple[str, str | None, bool]:
    """Return (severity, explanation, uncertain_flag)."""
    etype = event.event_type
    payload = event.raw_payload or {}

    if etype == "paste":
        text = payload.get("text", "")
        length = len(text) or payload.get("length", 0)
        # Fallback: if text missing, estimate based on code diff size
        if length == 0 and payload.get("afterCode") and payload.get("beforeCode"):
            length = abs(len(payload["afterCode"]) - len(payload["beforeCode"]))
        if length < 20:
            return "low", "Small paste (<20 chars) likely harmless", False
        if full_code_before and text in full_code_before:
            return "low", "Pasted text already existed in code (copy-move)", False
        if question_text and similarity_ratio(text, question_text) > 0.8:
            return "low", "Pasted text matches question statement", False
        if expected_output and similarity_ratio(text, expected_output) > 0.8:
            return "low", "Pasted text matches expected output", False
        return "medium", "Large paste detected - needs AI judgement", True

    if etype == "window_blur":
        away_ms = payload.get("blurDurationMs", 0)
        if away_ms < 2000:
            return "low", "Brief window switch (<2s)", False
        return "medium", "Window out of focus for >2s", True

    # default
    return "low", "Unhandled event type", False


# ---------------- Celery task ---------------- #

@shared_task(bind=True, name="analyze_suspicious_event")
def analyze_suspicious_event(self, event_id: int):  # noqa: D401
    """Fetch SuspiciousEvent by ID, run analysis, update row, emit alert."""
    # 1. Load event from DB
    with engine.begin() as conn:
        event_row = conn.execute(
            SuspiciousEvent.__table__.select().where(SuspiciousEvent.id == event_id)
        ).mappings().first()

    if not event_row:
        logger.error("SuspiciousEvent not found", event_id=event_id)
        return

    event = SuspiciousEvent(**event_row)

    # Gather extra context
    question_text: str | None = None
    expected_output: str | None = None
    full_code_before: str | None = None

    if event.event_type == "paste":
        # For paste we expect before/after code snapshots in payload
        full_code_before = event.raw_payload.get("beforeCode")
        # fetch latest question artifact
        with engine.begin() as conn:
            row = conn.execute(
                InterviewArtifact.__table__.select()
                .where(
                    (InterviewArtifact.session_id == event.session_id)
                    & (InterviewArtifact.artifact_type == "question")
                )
                .order_by(InterviewArtifact.created_at.desc())
                .limit(1)
            ).mappings().first()
        if row:
            question_text = row["content"]
        # expected output
        with engine.begin() as conn:
            row2 = conn.execute(
                InterviewArtifact.__table__.select()
                .where(
                    (InterviewArtifact.session_id == event.session_id)
                    & (InterviewArtifact.artifact_type == "expected_output")
                )
                .order_by(InterviewArtifact.created_at.desc())
                .limit(1)
            ).mappings().first()
        if row2:
            expected_output = row2["content"]

    severity, explanation, uncertain = apply_rules(event, question_text, expected_output, full_code_before)

    if uncertain:
        # Call small Gemini model
        try:
            model = get_chat_model(os.getenv("SOLUTION_MODEL", "google/gemini-2.5-flash-lite-preview-06-17"), 0)
            pasted_text = event.raw_payload.get("text", "")
            before_code = (full_code_before or "")[:4000]
            after_code = (event.raw_payload.get("afterCode", "") or "")[:4000]
            prompt = (
                "You are an internal monitoring assistant for a live coding interview. "
                "Given the event details, decide if this indicates cheating.\n"
                "Reply strictly in valid JSON with keys: severity (low|medium|high) and explanation.\n\n"
                f"EventType: {event.event_type}\n"
                f"PastedText: ```{pasted_text[:500]}```\n"
                f"CodeBefore (truncated 4k):\n```{before_code}```\n"
                f"CodeAfter (truncated 4k):\n```{after_code}```\n"
                f"Question: {question_text[:1000] if question_text else 'N/A'}\n"
                f"ExpectedOutput: {expected_output[:1000] if expected_output else 'N/A'}\n"
                "Use these heuristics:\n"
                "- If pasted text already existed, mark low.\n"
                "- If it closely matches question, mark low.\n"
                "- Large unrelated paste likely medium or high depending on length/complexity.\n"
                "- Always provide concise reasoning."
            )
            resp = model.invoke(prompt)
            parsed = json.loads(resp.content if hasattr(resp, "content") else str(resp))
            severity = parsed.get("severity", severity)
            explanation = parsed.get("explanation", explanation)
        except Exception as e:  # noqa: BLE001
            logger.warning("LLM analysis failed, fallback to rule result", error=str(e))

    # 3. Update DB row
    with engine.begin() as conn:
        conn.execute(
            update(SuspiciousEvent)
            .where(SuspiciousEvent.id == event_id)
            .values(severity=severity, explanation=explanation)
        )

    # 4. Emit alert to interviewer room via Socket.IO message queue
    room = f"session_{event.session_id}"
    import asyncio
    asyncio.run(
        sio.emit(
            "suspicious_alert",
            {
                "eventId": event.id,
                "eventType": event.event_type,
                "severity": severity,
                "explanation": explanation,
                "createdAt": str(event.created_at),
            },
            room=room,
        )
    )

    logger.info("Suspicious alert emitted", event_id=event_id, severity=severity)
