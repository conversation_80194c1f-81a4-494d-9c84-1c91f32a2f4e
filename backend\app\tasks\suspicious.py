"""Celery tasks for suspicious activity analysis."""
from __future__ import annotations

import os
from datetime import datetime
from typing import Tuple, Optional

from celery import shared_task
from sqlalchemy import update
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from sqlalchemy.dialects.postgresql import insert
from pydantic import BaseModel, Field
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser

from app.core.celery_app import celery_app  # ensures app is initialised
from app.core.logging import get_logger
from app.models.schemas import SuspiciousEvent, InterviewArtifact

from app.sockets import sio  # to emit alerts via message_queue
from app.services.ai_analysis_service import get_chat_model
from app.tasks.utils import similarity_ratio

logger = get_logger("suspicious_task")

# Configuration
MODEL_MAX_RETRIES = int(os.getenv("MODEL_MAX_RETRIES", "2"))

# Celery worker runs in a synchronous context, so we need a sync driver
raw_db_url = os.getenv("DATABASE_URL", "postgresql+asyncpg://user:password@postgres:5432/interview_platform")
if "+asyncpg" in raw_db_url:
    raw_db_url = raw_db_url.replace("+asyncpg", "+psycopg2")
DATABASE_URL = raw_db_url
engine = create_engine(DATABASE_URL, isolation_level="AUTOCOMMIT")

# Pydantic model for structured LLM response
class SuspiciousEventAnalysis(BaseModel):
    """Structured response from LLM analysis of suspicious events."""
    severity: str = Field(description="Severity level: low, medium, or high")
    explanation: str = Field(description="Brief explanation of the analysis reasoning")


# ---------------- Simple rule engine ---------------- #

def apply_rules(event: SuspiciousEvent, question_text: str | None, expected_output: str | None, full_code_before: str | None) -> Tuple[str, str | None, bool]:
    """Return (severity, explanation, uncertain_flag)."""
    etype = event.event_type
    payload = event.raw_payload or {}

    if etype == "paste":
        text = payload.get("text", "")
        before_code = payload.get("beforeCode", "")
        after_code = payload.get("afterCode", "")

        # Calculate the actual pasted content and length
        if text:
            # Direct paste text available
            pasted_content = text
            length = len(text)
        elif before_code and after_code:
            # Estimate pasted content from code difference
            length = abs(len(after_code) - len(before_code))
            # For analysis, use the new content that appeared
            if len(after_code) > len(before_code):
                # Code grew, likely a paste operation
                pasted_content = after_code[len(before_code):] if after_code.startswith(before_code) else after_code
            else:
                pasted_content = text  # Fallback to original text
        else:
            length = payload.get("length", 0)
            pasted_content = text

        # Rule-based analysis
        if length < 20:
            return "low", "Small paste (<20 chars) likely harmless", False

        # Check if it's a copy-move within existing code (only if we have the actual text)
        if text and full_code_before and text in full_code_before:
            return "low", "Pasted text already existed in code (copy-move)", False

        # Check similarity to question/expected output
        if question_text and pasted_content and similarity_ratio(pasted_content, question_text) > 0.8:
            return "low", "Pasted text matches question statement", False
        if expected_output and pasted_content and similarity_ratio(pasted_content, expected_output) > 0.8:
            return "low", "Pasted text matches expected output", False

        # Large paste with significant code change - needs AI analysis
        if length > 100:
            return "medium", f"Large paste detected ({length} chars) - needs AI analysis", True

        return "medium", "Paste detected - needs AI judgement", True

    if etype == "window_blur":
        away_ms = payload.get("blurDurationMs", 0)
        if away_ms < 2000:
            return "low", "Brief window switch (<2s)", False
        elif away_ms < 10000:  # 10 seconds
            return "medium", f"Window out of focus for {away_ms/1000:.1f}s", False
        else:
            return "high", f"Extended window out of focus for {away_ms/1000:.1f}s", False

    # default
    return "low", "Unhandled event type", False


# ---------------- Celery task ---------------- #

@shared_task(bind=True, name="analyze_suspicious_event")
def analyze_suspicious_event(self, event_id: int):  # noqa: D401
    """Fetch SuspiciousEvent by ID, run analysis, update row, emit alert."""
    # 1. Load event from DB
    with engine.begin() as conn:
        event_row = conn.execute(
            SuspiciousEvent.__table__.select().where(SuspiciousEvent.id == event_id)
        ).mappings().first()

    if not event_row:
        logger.error("SuspiciousEvent not found", event_id=event_id)
        return

    event = SuspiciousEvent(**event_row)

    # Gather extra context
    question_text: str | None = None
    expected_output: str | None = None
    full_code_before: str | None = None

    if event.event_type == "paste":
        # For paste we expect before/after code snapshots in payload
        full_code_before = event.raw_payload.get("beforeCode")
        # fetch latest question artifact
        with engine.begin() as conn:
            row = conn.execute(
                InterviewArtifact.__table__.select()
                .where(
                    (InterviewArtifact.session_id == event.session_id)
                    & (InterviewArtifact.artifact_type == "question")
                )
                .order_by(InterviewArtifact.created_at.desc())
                .limit(1)
            ).mappings().first()
        if row:
            question_text = row["content"]
        # expected output
        with engine.begin() as conn:
            row2 = conn.execute(
                InterviewArtifact.__table__.select()
                .where(
                    (InterviewArtifact.session_id == event.session_id)
                    & (InterviewArtifact.artifact_type == "expected_output")
                )
                .order_by(InterviewArtifact.created_at.desc())
                .limit(1)
            ).mappings().first()
        if row2:
            expected_output = row2["content"]

    severity, explanation, uncertain = apply_rules(event, question_text, expected_output, full_code_before)

    logger.info("Rule engine result",
               event_id=event.id,
               event_type=event.event_type,
               rule_severity=severity,
               rule_explanation=explanation,
               needs_llm_analysis=uncertain)

    if uncertain:
        # Call small Gemini model with structured output
        try:
            model = get_chat_model(os.getenv("SOLUTION_MODEL", "google/gemini-2.5-flash-lite-preview-06-17"), 0)
            pasted_text = event.raw_payload.get("text", "")
            before_code = (full_code_before or "")[:4000]
            after_code = (event.raw_payload.get("afterCode", "") or "")[:4000]

            # Create structured prompt using ChatPromptTemplate
            prompt_template = ChatPromptTemplate.from_messages([
                ("system",
                 "You are an AI assistant monitoring a live coding interview for potential cheating. "
                 "Analyze paste events to detect if candidates are using external assistance.\n\n"
                 "CRITICAL INDICATORS OF CHEATING:\n"
                 "- Complete function implementations appearing suddenly\n"
                 "- Complex algorithms or data structures not explained by candidate\n"
                 "- Code that solves the exact problem with optimal solutions\n"
                 "- Advanced techniques beyond typical interview level\n"
                 "- Large code blocks (>50 lines) pasted at once\n\n"
                 "SEVERITY LEVELS:\n"
                 "- 'high': Complete solutions, complex algorithms, or large code blocks that solve the problem\n"
                 "- 'medium': Substantial code snippets that provide significant progress\n"
                 "- 'low': Small snippets, comments, or code copied from the question\n\n"
                 "Respond with JSON: {{\"severity\": \"low/medium/high\", \"explanation\": \"brief reason\"}}"),
                ("user",
                 "PASTE EVENT ANALYSIS\n"
                 "===================\n"
                 "Event: {event_type}\n"
                 "Pasted Text: {pasted_text}\n\n"
                 "CODE BEFORE PASTE:\n{before_code}\n\n"
                 "CODE AFTER PASTE:\n{after_code}\n\n"
                 "INTERVIEW QUESTION:\n{question}\n\n"
                 "EXPECTED OUTPUT:\n{expected_output}\n\n"
                 "ANALYSIS INSTRUCTIONS:\n"
                 "1. Compare before/after code to see what was actually added\n"
                 "2. Determine if the pasted content provides a complete or partial solution\n"
                 "3. Consider the complexity and sophistication of the pasted code\n"
                 "4. If the paste contains function definitions, algorithms, or substantial logic, mark as HIGH\n"
                 "5. If it's just variable names, comments, or question text, mark as LOW")
            ])

            # Use JsonOutputParser for reliable JSON parsing
            parser = JsonOutputParser(pydantic_object=SuspiciousEventAnalysis)
            chain = prompt_template | model | parser
            chain_with_retry = chain.with_retry(stop_after_attempt=MODEL_MAX_RETRIES)

            # Invoke with retry mechanism
            logger.debug("Invoking suspicious event analysis chain",
                        event_id=event.id,
                        event_type=event.event_type,
                        max_retries=MODEL_MAX_RETRIES)

            result = chain_with_retry.invoke({
                "event_type": event.event_type,
                "pasted_text": pasted_text[:500],  # Truncate to avoid token limits
                "before_code": before_code,
                "after_code": after_code,
                "question": question_text[:1000] if question_text else "N/A",
                "expected_output": expected_output[:1000] if expected_output else "N/A"
            })

            # Validate and extract results from structured response
            if isinstance(result, dict):
                llm_severity = result.get("severity", "").lower()
                llm_explanation = result.get("explanation", "")

                # Validate severity value
                if llm_severity in ["low", "medium", "high"]:
                    severity = llm_severity
                    explanation = llm_explanation or explanation

                    logger.info("LLM analysis completed successfully",
                               event_id=event.id,
                               original_severity=apply_rules(event, question_text, expected_output, full_code_before)[0],
                               llm_severity=severity,
                               explanation_length=len(explanation))
                else:
                    logger.warning("LLM returned invalid severity, using rule result",
                                  event_id=event.id,
                                  invalid_severity=llm_severity,
                                  valid_options=["low", "medium", "high"])
            else:
                logger.warning("LLM returned non-dict response, using rule result",
                              event_id=event.id,
                              response_type=type(result).__name__)

        except Exception as e:  # noqa: BLE001
            logger.warning("LLM analysis failed, fallback to rule result",
                          error=str(e),
                          error_type=type(e).__name__,
                          event_id=event.id,
                          event_type=event.event_type)

    # 3. Update DB row
    with engine.begin() as conn:
        conn.execute(
            update(SuspiciousEvent)
            .where(SuspiciousEvent.id == event_id)
            .values(severity=severity, explanation=explanation)
        )

    # 4. Emit alert to interviewer room via Socket.IO message queue
    room = f"session_{event.session_id}"
    try:
        # Use asyncio.run() which handles event loop creation/cleanup automatically
        import asyncio

        async def emit_suspicious_alert():
            """Async function to emit the suspicious alert."""
            payload = {
                "eventId": event.id,
                "eventType": event.event_type,
                "severity": severity,
                "explanation": explanation,
                "createdAt": str(event.created_at),
            }

            # Check room members before emitting
            try:
                room_members = sio.manager.get_participants("/", room)  # Use default namespace "/"
                logger.info("Emitting suspicious_alert payload",
                           payload=payload,
                           room=room,
                           event_id=event.id,
                           room_member_count=len(room_members),
                           room_members=list(room_members))
            except Exception as e:
                logger.warning("Could not get room members", error=str(e))
                logger.info("Emitting suspicious_alert payload",
                           payload=payload,
                           room=room,
                           event_id=event.id)

            # Emit with detailed debugging
            result = await sio.emit("suspicious_alert", payload, room=room)
            logger.info("WebSocket emission result",
                       result=result,
                       event_id=event.id,
                       sio_connected=hasattr(sio, 'manager') and sio.manager is not None)

        # Debug Socket.IO instance state
        logger.info("Socket.IO instance debug info",
                   event_id=event.id,
                   sio_type=type(sio).__name__,
                   has_manager=hasattr(sio, 'manager'),
                   manager_type=type(sio.manager).__name__ if hasattr(sio, 'manager') else None,
                   message_queue_configured=hasattr(sio, 'message_queue') and sio.message_queue is not None,
                   message_queue_url=getattr(sio, 'message_queue', None))

        # Test Redis message queue first
        async def test_redis_emission():
            """Test if Redis message queue is working."""
            try:
                test_payload = {"test": "redis_queue_test", "timestamp": str(datetime.now())}
                await sio.emit("test_redis", test_payload, room=room)
                logger.info("Redis test emission sent", room=room, event_id=event.id)
            except Exception as e:
                logger.error("Redis test emission failed", error=str(e), event_id=event.id)

        # Emit both test and real alert
        asyncio.run(test_redis_emission())
        asyncio.run(emit_suspicious_alert())

        logger.info("Suspicious alert emitted successfully",
                   event_id=event.id,
                   severity=severity,
                   room=room,
                   session_id=event.session_id)

    except Exception as e:
        logger.error("Failed to emit suspicious alert",
                    error=str(e),
                    error_type=type(e).__name__,
                    event_id=event.id,
                    room=room,
                    session_id=event.session_id)
